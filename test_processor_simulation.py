#!/usr/bin/env python3
"""Simulate the email processor behavior with our fix."""

import imaplib
import email
import sqlite3
from datetime import datetime
import os

# Email configuration
EMAIL_IMAP_SERVER = "imap.gmail.com"
EMAIL_IMAP_PORT = 993
EMAIL_USERNAME = "<EMAIL>"
EMAIL_PASSWORD = "cozmlrykzeekvxmu"
EMAIL_USE_SSL = True

class SimpleEmailDatabase:
    """Simplified version of EmailDatabase for testing."""
    
    def __init__(self, db_path="test_email_agent.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize database tables."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Create sync_state table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS sync_state (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    last_sync_timestamp TEXT NOT NULL,
                    last_email_uid TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            # Create processed_emails table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS processed_emails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email_uid TEXT UNIQUE NOT NULL,
                    sender_email TEXT NOT NULL,
                    subject TEXT NOT NULL,
                    received_at TEXT NOT NULL,
                    processed_at TEXT NOT NULL,
                    response_sent BOOLEAN DEFAULT FALSE,
                    error_message TEXT,
                    thread_id TEXT,
                    created_at TEXT NOT NULL
                )
            """)
            
            conn.commit()
    
    def get_last_sync_timestamp(self):
        """Get the last sync timestamp."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT last_sync_timestamp FROM sync_state ORDER BY id DESC LIMIT 1"
            )
            result = cursor.fetchone()
            return result[0] if result else None
    
    def update_sync_timestamp(self, timestamp, last_email_uid=None):
        """Update the last sync timestamp."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            now = datetime.now().isoformat()
            
            cursor.execute("SELECT COUNT(*) FROM sync_state")
            count = cursor.fetchone()[0]
            
            if count == 0:
                cursor.execute("""
                    INSERT INTO sync_state (last_sync_timestamp, last_email_uid, created_at, updated_at)
                    VALUES (?, ?, ?, ?)
                """, (timestamp, last_email_uid, now, now))
            else:
                cursor.execute("""
                    UPDATE sync_state 
                    SET last_sync_timestamp = ?, last_email_uid = ?, updated_at = ?
                    WHERE id = (SELECT id FROM sync_state ORDER BY id DESC LIMIT 1)
                """, (timestamp, last_email_uid, now))
            
            conn.commit()
    
    def is_email_processed(self, email_uid):
        """Check if an email has already been processed."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute(
                "SELECT COUNT(*) FROM processed_emails WHERE email_uid = ?",
                (email_uid,)
            )
            count = cursor.fetchone()[0]
            return count > 0
    
    def mark_email_processed(self, email_uid, sender, subject, received_at):
        """Mark an email as processed."""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            now = datetime.now().isoformat()
            
            cursor.execute("""
                INSERT OR REPLACE INTO processed_emails 
                (email_uid, sender_email, subject, received_at, processed_at, 
                 response_sent, error_message, thread_id, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                email_uid, sender, subject, received_at, now,
                True, None, f"test_{email_uid}", now
            ))
            
            conn.commit()

def get_new_emails_old_logic(connection, since_timestamp=None):
    """Old logic - UNSEEN only."""
    connection.select("INBOX")
    
    search_criteria = "UNSEEN"
    if since_timestamp:
        dt = datetime.fromisoformat(since_timestamp.replace('Z', '+00:00'))
        date_str = dt.strftime("%d-%b-%Y")
        search_criteria = f'(UNSEEN SINCE "{date_str}")'
    
    print(f"OLD LOGIC - Search criteria: {search_criteria}")
    status, message_ids = connection.search(None, search_criteria)
    
    if status != 'OK':
        return []
    
    emails = []
    message_ids = message_ids[0].split()
    print(f"OLD LOGIC - Found {len(message_ids)} emails")
    
    return message_ids

def get_new_emails_new_logic(connection, since_timestamp=None):
    """New logic - date-based search."""
    connection.select("INBOX")
    
    if since_timestamp:
        dt = datetime.fromisoformat(since_timestamp.replace('Z', '+00:00'))
        date_str = dt.strftime("%d-%b-%Y")
        search_criteria = f'SINCE "{date_str}"'
    else:
        search_criteria = "UNSEEN"
    
    print(f"NEW LOGIC - Search criteria: {search_criteria}")
    status, message_ids = connection.search(None, search_criteria)
    
    if status != 'OK':
        return []
    
    emails = []
    message_ids = message_ids[0].split()
    print(f"NEW LOGIC - Found {len(message_ids)} emails")
    
    return message_ids

def test_processor_simulation():
    """Test the processor simulation."""
    print("🧪 Testing Email Processor Simulation")
    print("=" * 50)
    
    # Clean up test database
    if os.path.exists("test_email_agent.db"):
        os.remove("test_email_agent.db")
    
    database = SimpleEmailDatabase()
    
    try:
        # Connect to email server
        if EMAIL_USE_SSL:
            connection = imaplib.IMAP4_SSL(EMAIL_IMAP_SERVER, EMAIL_IMAP_PORT)
        else:
            connection = imaplib.IMAP4(EMAIL_IMAP_SERVER, EMAIL_IMAP_PORT)
        
        connection.login(EMAIL_USERNAME, EMAIL_PASSWORD)
        print("✅ IMAP connection successful")
        
        # Test 1: First run (no sync timestamp)
        print("\n📧 TEST 1: First run (no sync timestamp)")
        last_sync = database.get_last_sync_timestamp()
        print(f"Last sync timestamp: {last_sync}")
        
        old_emails = get_new_emails_old_logic(connection, last_sync)
        new_emails = get_new_emails_new_logic(connection, last_sync)
        
        print(f"Old logic found: {len(old_emails)} emails")
        print(f"New logic found: {len(new_emails)} emails")
        
        # Simulate processing with new logic
        if new_emails:
            print("Processing emails with new logic...")
            for msg_id in new_emails:
                msg_id_str = msg_id.decode()
                if not database.is_email_processed(msg_id_str):
                    # Fetch email details
                    status, msg_data = connection.fetch(msg_id, '(RFC822)')
                    if status == 'OK':
                        email_message = email.message_from_bytes(msg_data[0][1])
                        subject = email_message.get('Subject', '')
                        sender = email_message.get('From', '')
                        date_str = email_message.get('Date', '')
                        
                        print(f"  Processing: {subject} from {sender}")
                        database.mark_email_processed(msg_id_str, sender, subject, date_str)
                else:
                    print(f"  Email {msg_id_str} already processed, skipping")
            
            # Update sync timestamp
            current_time = datetime.now().isoformat()
            database.update_sync_timestamp(current_time, new_emails[-1].decode())
        
        # Test 2: Second run (with sync timestamp)
        print("\n📧 TEST 2: Second run (with sync timestamp)")

        # Simulate having a sync timestamp from yesterday to trigger date-based search
        from datetime import timedelta
        yesterday = (datetime.now() - timedelta(days=1)).isoformat()
        database.update_sync_timestamp(yesterday, "1")

        last_sync = database.get_last_sync_timestamp()
        print(f"Last sync timestamp: {last_sync}")

        old_emails = get_new_emails_old_logic(connection, last_sync)
        new_emails = get_new_emails_new_logic(connection, last_sync)
        
        print(f"Old logic found: {len(old_emails)} emails")
        print(f"New logic found: {len(new_emails)} emails")
        
        # Check if emails would be skipped due to already processed
        if new_emails:
            print("Checking if emails are already processed...")
            for msg_id in new_emails:
                msg_id_str = msg_id.decode()
                is_processed = database.is_email_processed(msg_id_str)
                print(f"  Email {msg_id_str}: {'Already processed' if is_processed else 'Would be processed'}")
        
        connection.logout()
        print("\n✅ Simulation completed successfully")
        
        # Summary
        print("\n📊 SUMMARY:")
        print("The fix changes the search from UNSEEN-only to date-based search.")
        print("This allows the agent to find emails that were marked as read by previous fetches.")
        print("The database tracking prevents duplicate processing.")
        
    except Exception as e:
        print(f"❌ Simulation failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_processor_simulation()
