"""Configuration classes for the MCP (Multi-Channel Processing) module."""

from pydantic import BaseModel, Field

class MCPConfig(BaseModel):
    """Configuration for the MCP (Multi-Channel Processing) module."""
    
    http_address: str = Field()
    description: str = Field()

mcp_integrations: list[MCPConfig] = [
    MCPConfig(
        http_address="http://localhost:5000/api/agent",
        description="HTTP endpoint for agent communication"
    )
]