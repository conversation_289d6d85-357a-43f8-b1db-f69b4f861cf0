#!/usr/bin/env python3
"""Test the fixed search logic."""

import imaplib
import email
from datetime import datetime, timedelta

# Email configuration
EMAIL_IMAP_SERVER = "imap.gmail.com"
EMAIL_IMAP_PORT = 993
EMAIL_USERNAME = "<EMAIL>"
EMAIL_PASSWORD = "cozmlrykzeekvxmu"
EMAIL_USE_SSL = True

def test_fixed_search_logic():
    """Test the fixed search logic that uses date-based search."""
    print("🔍 Testing fixed search logic...")
    
    try:
        # Connect to IMAP server
        if EMAIL_USE_SSL:
            connection = imaplib.IMAP4_SSL(EMAIL_IMAP_SERVER, EMAIL_IMAP_PORT)
        else:
            connection = imaplib.IMAP4(EMAIL_IMAP_SERVER, EMAIL_IMAP_PORT)
        
        # Login
        connection.login(EMAIL_USERNAME, EMAIL_PASSWORD)
        print("✅ IMAP connection successful")
        
        # Select inbox
        connection.select("INBOX")
        
        # Simulate the old logic (UNSEEN only)
        print("\n🔍 OLD LOGIC - UNSEEN only:")
        status, unseen_ids = connection.search(None, "UNSEEN")
        if status == 'OK':
            unseen_count = len(unseen_ids[0].split()) if unseen_ids[0] else 0
            print(f"   Found {unseen_count} unseen emails")
        
        # Simulate the new logic (date-based search)
        print("\n🔍 NEW LOGIC - Date-based search:")
        
        # Test with different time ranges
        test_dates = [
            ("Today", datetime.now()),
            ("Yesterday", datetime.now() - timedelta(days=1)),
            ("3 days ago", datetime.now() - timedelta(days=3)),
            ("1 week ago", datetime.now() - timedelta(days=7)),
        ]
        
        for label, since_date in test_dates:
            date_str = since_date.strftime("%d-%b-%Y")
            search_criteria = f'SINCE "{date_str}"'
            
            print(f"   {label} ({date_str}):")
            status, date_ids = connection.search(None, search_criteria)
            if status == 'OK':
                date_count = len(date_ids[0].split()) if date_ids[0] else 0
                print(f"     Found {date_count} emails")
                
                if date_count > 0:
                    # Show details of found emails
                    msg_ids = date_ids[0].split()
                    for i, msg_id in enumerate(msg_ids[-3:]):  # Show last 3
                        try:
                            status, msg_data = connection.fetch(msg_id, '(BODY[HEADER.FIELDS (FROM SUBJECT DATE)])')
                            if status == 'OK':
                                header_data = msg_data[0][1].decode('utf-8')
                                lines = header_data.strip().split('\n')
                                subject = next((line.split(':', 1)[1].strip() for line in lines if line.startswith('Subject:')), 'No subject')
                                sender = next((line.split(':', 1)[1].strip() for line in lines if line.startswith('From:')), 'No sender')
                                date = next((line.split(':', 1)[1].strip() for line in lines if line.startswith('Date:')), 'No date')
                                print(f"       Email {i+1}: {subject} from {sender} ({date})")
                        except Exception as e:
                            print(f"       Error fetching email {msg_id}: {e}")
        
        # Test the exact logic from the fixed code
        print("\n🔍 SIMULATING FIXED CODE LOGIC:")
        
        # Simulate having a last sync timestamp (like the agent would have)
        last_sync_timestamp = (datetime.now() - timedelta(days=1)).isoformat()
        print(f"   Simulating last_sync_timestamp: {last_sync_timestamp}")
        
        # Convert to IMAP date format (like the fixed code does)
        dt = datetime.fromisoformat(last_sync_timestamp.replace('Z', '+00:00'))
        date_str = dt.strftime("%d-%b-%Y")
        search_criteria = f'SINCE "{date_str}"'
        
        print(f"   Search criteria: {search_criteria}")
        status, found_ids = connection.search(None, search_criteria)
        if status == 'OK':
            found_count = len(found_ids[0].split()) if found_ids[0] else 0
            print(f"   Found {found_count} emails since last sync")
            
            if found_count > 0:
                print("   ✅ The fixed logic would find emails!")
            else:
                print("   ❌ The fixed logic would still find no emails")
        
        # Test with no timestamp (first run scenario)
        print("\n🔍 FIRST RUN SCENARIO (no timestamp):")
        search_criteria = "UNSEEN"
        print(f"   Search criteria: {search_criteria}")
        status, first_run_ids = connection.search(None, search_criteria)
        if status == 'OK':
            first_run_count = len(first_run_ids[0].split()) if first_run_ids[0] else 0
            print(f"   Found {first_run_count} emails on first run")
        
        # Close connection
        connection.logout()
        print("\n✅ Test completed successfully")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_fixed_search_logic()
