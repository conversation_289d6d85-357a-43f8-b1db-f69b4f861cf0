# Email Whitelist Configuration

The email agent supports email address whitelisting to restrict processing to only trusted senders. This feature provides security by ensuring the AI agent only responds to emails from approved sources.

## Configuration

### Environment Variables

Add the following variables to your `.env` file:

```bash
# Enable or disable whitelist filtering
EMAIL_WHITELIST_ENABLED=true

# Comma-separated list of allowed email addresses and domains
EMAIL_WHITELIST=<EMAIL>,@trusted-domain.com,<EMAIL>
```

### Configuration Options

- **EMAIL_WHITELIST_ENABLED**: Set to `true` to enable whitelist filtering, `false` to disable
- **EMAIL_WHITELIST**: Comma-separated list of allowed email addresses and domain patterns

## Whitelist Entry Types

### 1. Exact Email Addresses

Specify complete email addresses that should be allowed:

```bash
EMAIL_WHITELIST=<EMAIL>,<EMAIL>,<EMAIL>
```

### 2. Domain Patterns

Allow all emails from specific domains by prefixing with `@`:

```bash
<PERSON>MAIL_WHITELIST=@company.com,@trusted-partner.org,@internal.net
```

### 3. Mixed Configuration

Combine exact emails and domain patterns:

```bash
EMAIL_WHITELIST=<EMAIL>,@company.com,@partner.org,<EMAIL>
```

## Behavior

### When Whitelist is Enabled

- **Allowed emails**: Processed normally by the AI agent
- **Non-whitelisted emails**: Skipped entirely (no AI processing, no response sent)
- **Malformed emails**: Rejected (invalid sender addresses)

### When Whitelist is Disabled

- All emails are processed regardless of sender

### Logging

The system logs whitelist decisions:

```
INFO: Email allowed by exact match: <EMAIL>
INFO: Email allowed by domain pattern: <EMAIL> (@trusted.com)
INFO: Email denied by whitelist: <EMAIL>
INFO: <NAME_EMAIL> not in whitelist - skipping processing
```

## Case Sensitivity

Email matching is **case-insensitive**:
- `<EMAIL>` matches `<EMAIL>`
- `@DOMAIN.COM` matches emails from `<EMAIL>`

## Email Format Support

The whitelist handles various email formats:

```
✅ <EMAIL>
✅ User Name <<EMAIL>>
✅ "User Name" <<EMAIL>>
❌ invalid-email
❌ @domain-without-user
❌ user@domain-without-tld
```

## Monitoring and Statistics

### Admin Panel

The web admin panel (`/api/email/status`) includes whitelist information:

```json
{
  "whitelist": {
    "whitelist_enabled": true,
    "whitelist_entries": {
      "exact_emails": ["<EMAIL>"],
      "domain_patterns": ["@trusted.com"],
      "total_count": 2
    },
    "rejection_stats": {
      "total_rejected": 5,
      "recent_rejections": [
        {
          "sender": "<EMAIL>",
          "subject": "Unwanted email",
          "received_at": "2024-01-15T10:30:00"
        }
      ]
    }
  }
}
```

### Database Tracking

Rejected emails are stored in the database with:
- `response_sent = FALSE`
- `error_message = "Email sender not in whitelist"`

## Security Considerations

### Best Practices

1. **Enable whitelist in production**: Always use whitelisting in production environments
2. **Regular review**: Periodically review whitelist entries and rejection logs
3. **Minimal permissions**: Only add necessary email addresses/domains
4. **Monitor rejections**: Check rejection logs for legitimate emails being blocked

### Domain vs. Exact Email Trade-offs

- **Domain patterns** (`@company.com`): Convenient but less secure
- **Exact emails**: More secure but requires maintenance for new users

## Example Configurations

### Corporate Environment
```bash
EMAIL_WHITELIST_ENABLED=true
EMAIL_WHITELIST=@mycompany.com,@partner1.com,@partner2.org,<EMAIL>
```

### Personal Assistant
```bash
EMAIL_WHITELIST_ENABLED=true
EMAIL_WHITELIST=<EMAIL>,<EMAIL>,@family-domain.com
```

### Development/Testing
```bash
EMAIL_WHITELIST_ENABLED=false
EMAIL_WHITELIST=
```

## Troubleshooting

### Common Issues

1. **Legitimate emails being rejected**
   - Check if sender is in whitelist
   - Verify email format in logs
   - Add sender to whitelist if appropriate

2. **Whitelist not working**
   - Ensure `EMAIL_WHITELIST_ENABLED=true`
   - Check for typos in email addresses
   - Verify domain patterns start with `@`

3. **Performance concerns**
   - Whitelist checking is very fast (O(1) for exact matches, O(n) for domains)
   - No significant performance impact even with large whitelists

### Debug Information

Check the application logs for whitelist initialization:

```
INFO: Email whitelist enabled with 3 entries: ['<EMAIL>'] exact emails, ['@trusted.com', '@partner.org'] domain patterns
```

Or for disabled whitelist:

```
INFO: Email whitelist disabled - all emails will be processed
```

## API Integration

### Get Whitelist Status

```python
from email_agent import EmailProcessor

processor = EmailProcessor()
status = processor.get_whitelist_status()
print(status)
```

### Check Email Manually

```python
from email_agent.whitelist import EmailWhitelist
from config.settings import settings

whitelist = EmailWhitelist(
    whitelist_string=settings.email_whitelist,
    enabled=settings.email_whitelist_enabled
)

is_allowed = whitelist.is_allowed("<EMAIL>")
```
