"""SQLite database management for email sync tracking."""

import sqlite3
import logging
from datetime import datetime
from typing import Optional, List, Dict, Any, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)


class EmailDatabase:
    """Manages SQLite database for email sync tracking."""
    
    def __init__(self, db_path: str = "email_agent.db"):
        """Initialize database connection.
        
        Args:
            db_path: Path to SQLite database file
        """
        self.db_path = Path(db_path)
        self.init_database()
    
    def init_database(self):
        """Initialize database tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create sync_state table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS sync_state (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        last_sync_timestamp TEXT NOT NULL,
                        last_email_uid TEXT,
                        created_at TEXT NOT NULL,
                        updated_at TEXT NOT NULL
                    )
                """)
                
                # Create processed_emails table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS processed_emails (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email_uid TEXT UNIQUE NOT NULL,
                        sender_email TEXT NOT NULL,
                        subject TEXT NOT NULL,
                        received_at TEXT NOT NULL,
                        processed_at TEXT NOT NULL,
                        response_sent BOOLEAN DEFAULT FALSE,
                        error_message TEXT,
                        thread_id TEXT,
                        created_at TEXT NOT NULL
                    )
                """)

                # Create email_attachments table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS email_attachments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email_uid TEXT NOT NULL,
                        filename TEXT NOT NULL,
                        safe_filename TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        content_type TEXT NOT NULL,
                        file_size INTEGER NOT NULL,
                        unique_id TEXT NOT NULL,
                        is_relevant BOOLEAN DEFAULT TRUE,
                        classification_reason TEXT,
                        created_at TEXT NOT NULL,
                        FOREIGN KEY (email_uid) REFERENCES processed_emails (email_uid)
                    )
                """)
                
                conn.commit()
                logger.info("Database initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def get_last_sync_timestamp(self) -> Optional[str]:
        """Get the last sync timestamp.
        
        Returns:
            Last sync timestamp as ISO string or None if no sync yet
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT last_sync_timestamp FROM sync_state ORDER BY id DESC LIMIT 1"
                )
                result = cursor.fetchone()
                return result[0] if result else None
                
        except Exception as e:
            logger.error(f"Failed to get last sync timestamp: {e}")
            return None
    
    def update_sync_timestamp(self, timestamp: str, last_email_uid: Optional[str] = None):
        """Update the last sync timestamp.
        
        Args:
            timestamp: ISO timestamp string
            last_email_uid: UID of the last processed email
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                
                # Check if we have any sync state records
                cursor.execute("SELECT COUNT(*) FROM sync_state")
                count = cursor.fetchone()[0]
                
                if count == 0:
                    # Insert first record
                    cursor.execute("""
                        INSERT INTO sync_state (last_sync_timestamp, last_email_uid, created_at, updated_at)
                        VALUES (?, ?, ?, ?)
                    """, (timestamp, last_email_uid, now, now))
                else:
                    # Update existing record
                    cursor.execute("""
                        UPDATE sync_state 
                        SET last_sync_timestamp = ?, last_email_uid = ?, updated_at = ?
                        WHERE id = (SELECT id FROM sync_state ORDER BY id DESC LIMIT 1)
                    """, (timestamp, last_email_uid, now))
                
                conn.commit()
                logger.info(f"Updated sync timestamp to {timestamp}")
                
        except Exception as e:
            logger.error(f"Failed to update sync timestamp: {e}")
            raise
    
    def is_email_processed(self, email_uid: str) -> bool:
        """Check if an email has already been processed.
        
        Args:
            email_uid: Email UID to check
            
        Returns:
            True if email was already processed
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(
                    "SELECT COUNT(*) FROM processed_emails WHERE email_uid = ?",
                    (email_uid,)
                )
                count = cursor.fetchone()[0]
                return count > 0
                
        except Exception as e:
            logger.error(f"Failed to check if email processed: {e}")
            return False
    
    def mark_email_processed(self, email_data: Dict[str, Any], thread_id: str, 
                           response_sent: bool = False, error_message: Optional[str] = None):
        """Mark an email as processed.
        
        Args:
            email_data: Dictionary containing email information
            thread_id: Thread ID used for processing
            response_sent: Whether response was sent successfully
            error_message: Error message if processing failed
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO processed_emails 
                    (email_uid, sender_email, subject, received_at, processed_at, 
                     response_sent, error_message, thread_id, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    email_data['uid'],
                    email_data['sender'],
                    email_data['subject'],
                    email_data['received_at'],
                    now,
                    response_sent,
                    error_message,
                    thread_id,
                    now
                ))
                
                conn.commit()
                logger.info(f"Marked email {email_data['uid']} as processed")

        except Exception as e:
            logger.error(f"Failed to mark email as processed: {e}")
            raise

    def save_email_attachments(self, email_uid: str, attachments: List[Dict[str, Any]]):
        """Save email attachment metadata to database.

        Args:
            email_uid: Email UID
            attachments: List of attachment metadata dictionaries
        """
        if not attachments:
            return

        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                now = datetime.now().isoformat()

                for attachment in attachments:
                    cursor.execute("""
                        INSERT INTO email_attachments
                        (email_uid, filename, safe_filename, file_path, content_type,
                         file_size, unique_id, is_relevant, classification_reason, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        email_uid,
                        attachment.get('filename', ''),
                        attachment.get('safe_filename', ''),
                        attachment.get('file_path', ''),
                        attachment.get('content_type', ''),
                        attachment.get('file_size', 0),
                        attachment.get('unique_id', ''),
                        attachment.get('is_relevant', True),
                        attachment.get('classification_reason', ''),
                        now
                    ))

                conn.commit()
                logger.info(f"Saved {len(attachments)} attachments for email {email_uid}")

        except Exception as e:
            logger.error(f"Failed to save email attachments: {e}")
            raise

    def get_email_attachments(self, email_uid: str) -> List[Dict[str, Any]]:
        """Get attachment metadata for an email.

        Args:
            email_uid: Email UID

        Returns:
            List of attachment metadata dictionaries
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT filename, safe_filename, file_path, content_type,
                           file_size, unique_id, is_relevant, classification_reason
                    FROM email_attachments
                    WHERE email_uid = ? AND is_relevant = TRUE
                    ORDER BY created_at
                """, (email_uid,))

                rows = cursor.fetchall()
                attachments = []

                for row in rows:
                    attachments.append({
                        'filename': row[0],
                        'safe_filename': row[1],
                        'file_path': row[2],
                        'content_type': row[3],
                        'file_size': row[4],
                        'unique_id': row[5],
                        'is_relevant': bool(row[6]),
                        'classification_reason': row[7]
                    })

                return attachments

        except Exception as e:
            logger.error(f"Failed to get email attachments: {e}")
            return []

    def get_attachment_by_id(self, unique_id: str) -> Optional[Dict[str, Any]]:
        """Get attachment metadata by unique ID.

        Args:
            unique_id: Unique attachment ID

        Returns:
            Attachment metadata dictionary or None
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT email_uid, filename, safe_filename, file_path, content_type,
                           file_size, unique_id, is_relevant, classification_reason
                    FROM email_attachments
                    WHERE unique_id = ? AND is_relevant = TRUE
                """, (unique_id,))

                row = cursor.fetchone()
                if row:
                    return {
                        'email_uid': row[0],
                        'filename': row[1],
                        'safe_filename': row[2],
                        'file_path': row[3],
                        'content_type': row[4],
                        'file_size': row[5],
                        'unique_id': row[6],
                        'is_relevant': bool(row[7]),
                        'classification_reason': row[8]
                    }

                return None

        except Exception as e:
            logger.error(f"Failed to get attachment by ID: {e}")
            return None
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get email processing statistics.
        
        Returns:
            Dictionary with processing statistics
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Total processed emails
                cursor.execute("SELECT COUNT(*) FROM processed_emails")
                total_processed = cursor.fetchone()[0]
                
                # Successful responses
                cursor.execute("SELECT COUNT(*) FROM processed_emails WHERE response_sent = TRUE")
                successful_responses = cursor.fetchone()[0]
                
                # Failed processing
                cursor.execute("SELECT COUNT(*) FROM processed_emails WHERE error_message IS NOT NULL")
                failed_processing = cursor.fetchone()[0]
                
                # Last sync time
                last_sync = self.get_last_sync_timestamp()
                
                return {
                    "total_processed": total_processed,
                    "successful_responses": successful_responses,
                    "failed_processing": failed_processing,
                    "last_sync": last_sync,
                    "success_rate": (successful_responses / total_processed * 100) if total_processed > 0 else 0
                }
                
        except Exception as e:
            logger.error(f"Failed to get processing stats: {e}")
            return {
                "total_processed": 0,
                "successful_responses": 0,
                "failed_processing": 0,
                "last_sync": None,
                "success_rate": 0
            }

    def get_whitelist_rejection_stats(self) -> Dict[str, Any]:
        """Get statistics about emails rejected by whitelist.

        Returns:
            Dictionary with whitelist rejection statistics
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Total whitelist rejections
                cursor.execute("""
                    SELECT COUNT(*) FROM processed_emails
                    WHERE error_message = 'Email sender not in whitelist'
                """)
                total_rejected = cursor.fetchone()[0]

                # Recent rejections (last 24 hours)
                cursor.execute("""
                    SELECT sender, subject, received_at
                    FROM processed_emails
                    WHERE error_message = 'Email sender not in whitelist'
                    AND datetime(received_at) > datetime('now', '-1 day')
                    ORDER BY received_at DESC
                    LIMIT 10
                """)
                recent_rejections = [
                    {
                        "sender": row[0],
                        "subject": row[1],
                        "received_at": row[2]
                    }
                    for row in cursor.fetchall()
                ]

                return {
                    "total_rejected": total_rejected,
                    "recent_rejections": recent_rejections
                }

        except Exception as e:
            logger.error(f"Failed to get whitelist rejection stats: {e}")
            return {
                "total_rejected": 0,
                "recent_rejections": []
            }
