"""Email Agent Package for processing AI instructions via email.

This package provides functionality to:
- Connect to email accounts via IMAP/SMTP
- Process incoming emails with AI instructions
- Send responses back via email
- Track email processing state in SQLite database
"""

from .connection import EmailConnection
from .processor import EmailProcessor
from .database import EmailDatabase
from .scheduler import EmailScheduler
from .whitelist import Email<PERSON>hitelist

__version__ = "1.0.0"
__all__ = ["EmailConnection", "EmailProcessor", "EmailDatabase", "EmailScheduler", "EmailWhitelist"]
