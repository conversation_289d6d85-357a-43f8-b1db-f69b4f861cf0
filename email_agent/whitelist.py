"""Email whitelist validation functionality."""

import re
import logging
from typing import List, Set
from email.utils import parseaddr

logger = logging.getLogger(__name__)


class EmailWhitelist:
    """Handles email address whitelist validation."""
    
    def __init__(self, whitelist_string: str = "", enabled: bool = False):
        """Initialize the email whitelist.
        
        Args:
            whitelist_string: Comma-separated string of allowed emails/domains
            enabled: Whether whitelist filtering is enabled
        """
        self.enabled = enabled
        self.exact_emails: Set[str] = set()
        self.domain_patterns: Set[str] = set()
        
        if enabled and whitelist_string:
            self._parse_whitelist(whitelist_string)
    
    def _parse_whitelist(self, whitelist_string: str) -> None:
        """Parse the whitelist string into exact emails and domain patterns.
        
        Args:
            whitelist_string: Comma-separated whitelist entries
        """
        entries = [entry.strip().lower() for entry in whitelist_string.split(',') if entry.strip()]
        
        for entry in entries:
            if not entry:
                continue
                
            # Domain pattern (starts with @)
            if entry.startswith('@'):
                domain = entry[1:]  # Remove the @ prefix
                if self._is_valid_domain(domain):
                    self.domain_patterns.add(domain)
                    logger.info(f"Added domain pattern to whitelist: @{domain}")
                else:
                    logger.warning(f"Invalid domain pattern in whitelist: {entry}")
            
            # Exact email address
            elif self._is_valid_email(entry):
                self.exact_emails.add(entry)
                logger.info(f"Added exact email to whitelist: {entry}")
            else:
                logger.warning(f"Invalid email address in whitelist: {entry}")
        
        logger.info(f"Whitelist initialized: {len(self.exact_emails)} exact emails, "
                   f"{len(self.domain_patterns)} domain patterns")
    
    def _is_valid_email(self, email: str) -> bool:
        """Check if an email address is valid.
        
        Args:
            email: Email address to validate
            
        Returns:
            True if email is valid
        """
        try:
            # Use email.utils.parseaddr for basic validation
            name, addr = parseaddr(email)
            if not addr or '@' not in addr:
                return False
            
            # Additional regex validation for more strict checking
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            return bool(re.match(email_pattern, addr))
        except Exception:
            return False
    
    def _is_valid_domain(self, domain: str) -> bool:
        """Check if a domain is valid.
        
        Args:
            domain: Domain to validate
            
        Returns:
            True if domain is valid
        """
        try:
            # Basic domain validation
            domain_pattern = r'^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            return bool(re.match(domain_pattern, domain))
        except Exception:
            return False
    
    def _extract_email_address(self, sender: str) -> str:
        """Extract clean email address from sender string.
        
        Args:
            sender: Sender string (may include name and email)
            
        Returns:
            Clean email address or empty string if invalid
        """
        try:
            # <AUTHOR> <EMAIL>" format
            name, email = parseaddr(sender)
            return email.lower().strip() if email else ""
        except Exception as e:
            logger.warning(f"Failed to extract email from sender '{sender}': {e}")
            return ""
    
    def _extract_domain(self, email: str) -> str:
        """Extract domain from email address.
        
        Args:
            email: Email address
            
        Returns:
            Domain part of email or empty string if invalid
        """
        try:
            if '@' in email:
                return email.split('@')[1].lower()
            return ""
        except Exception:
            return ""
    
    def is_allowed(self, sender: str) -> bool:
        """Check if a sender email address is allowed by the whitelist.
        
        Args:
            sender: Sender email address (may include name)
            
        Returns:
            True if sender is allowed, False otherwise
        """
        # If whitelist is disabled, allow all emails
        if not self.enabled:
            return True
        
        # If no whitelist entries, deny all (when enabled)
        if not self.exact_emails and not self.domain_patterns:
            logger.warning("Email whitelist is enabled but empty - denying all emails")
            return False
        
        # Extract clean email address
        email = self._extract_email_address(sender)
        if not email:
            logger.warning(f"Could not extract valid email from sender: {sender}")
            return False
        
        # Check exact email match
        if email in self.exact_emails:
            logger.debug(f"Email allowed by exact match: {email}")
            return True
        
        # Check domain pattern match
        domain = self._extract_domain(email)
        if domain and domain in self.domain_patterns:
            logger.debug(f"Email allowed by domain pattern: {email} (@{domain})")
            return True
        
        # Not in whitelist
        logger.info(f"Email denied by whitelist: {email}")
        return False
    
    def get_whitelist_info(self) -> dict:
        """Get information about the current whitelist configuration.
        
        Returns:
            Dictionary with whitelist information
        """
        return {
            "enabled": self.enabled,
            "exact_emails": list(self.exact_emails),
            "domain_patterns": [f"@{domain}" for domain in self.domain_patterns],
            "total_entries": len(self.exact_emails) + len(self.domain_patterns)
        }
