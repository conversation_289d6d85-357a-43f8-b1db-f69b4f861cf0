#!/usr/bin/env python3
"""Simple test to check email connection without full dependencies."""

import imaplib
import email
import os
from datetime import datetime

# Email configuration from .env
EMAIL_IMAP_SERVER = "imap.gmail.com"
EMAIL_IMAP_PORT = 993
EMAIL_USERNAME = "<EMAIL>"
EMAIL_PASSWORD = "cozmlrykzeekvxmu"
EMAIL_USE_SSL = True

def test_email_connection():
    """Test basic email connection and search."""
    print("🔍 Testing email connection...")
    
    try:
        # Connect to IMAP server
        if EMAIL_USE_SSL:
            connection = imaplib.IMAP4_SSL(EMAIL_IMAP_SERVER, EMAIL_IMAP_PORT)
        else:
            connection = imaplib.IMAP4(EMAIL_IMAP_SERVER, EMAIL_IMAP_PORT)
        
        # Login
        connection.login(EMAIL_USERNAME, EMAIL_PASSWORD)
        print("✅ IMAP connection successful")
        
        # Select inbox
        connection.select("INBOX")
        
        # Test different search criteria
        print("\n🔍 Testing search criteria...")
        
        # 1. Search for UNSEEN emails
        print("1. Searching for UNSEEN emails:")
        status, unseen_ids = connection.search(None, "UNSEEN")
        if status == 'OK':
            unseen_count = len(unseen_ids[0].split()) if unseen_ids[0] else 0
            print(f"   Found {unseen_count} unseen emails")
        
        # 2. Search for ALL emails
        print("2. Searching for ALL emails:")
        status, all_ids = connection.search(None, "ALL")
        if status == 'OK':
            all_count = len(all_ids[0].split()) if all_ids[0] else 0
            print(f"   Found {all_count} total emails")
        
        # 3. Search for emails from today
        today = datetime.now().strftime("%d-%b-%Y")
        print(f"3. Searching for emails since {today}:")
        status, today_ids = connection.search(None, f'SINCE "{today}"')
        if status == 'OK':
            today_count = len(today_ids[0].split()) if today_ids[0] else 0
            print(f"   Found {today_count} emails since today")
        
        # 4. Search for emails from last 7 days
        from datetime import timedelta
        week_ago = (datetime.now() - timedelta(days=7)).strftime("%d-%b-%Y")
        print(f"4. Searching for emails since {week_ago}:")
        status, week_ids = connection.search(None, f'SINCE "{week_ago}"')
        if status == 'OK':
            week_count = len(week_ids[0].split()) if week_ids[0] else 0
            print(f"   Found {week_count} emails since {week_ago}")
        
        # Test the behavior of fetching emails and how it affects SEEN status
        if all_count > 0:
            print(f"\n📧 Testing email fetch behavior...")
            all_msg_ids = all_ids[0].split()

            if len(all_msg_ids) > 0:
                msg_id = all_msg_ids[-1]  # Get the latest email
                print(f"Testing with email ID: {msg_id.decode()}")

                # Check flags before any fetch
                status, flags = connection.fetch(msg_id, '(FLAGS)')
                if status == 'OK':
                    flags_str = str(flags[0][1])
                    is_seen_before = '\\Seen' in flags_str
                    print(f"   Seen flag BEFORE fetch: {is_seen_before}")

                # Now fetch the full email (this is what the agent does)
                print("   Fetching full email with RFC822...")
                status, msg_data = connection.fetch(msg_id, '(RFC822)')
                if status == 'OK':
                    email_message = email.message_from_bytes(msg_data[0][1])
                    subject = email_message.get('Subject', '')
                    sender = email_message.get('From', '')
                    print(f"   Subject: {subject}")
                    print(f"   From: {sender}")

                # Check flags after RFC822 fetch
                status, flags = connection.fetch(msg_id, '(FLAGS)')
                if status == 'OK':
                    flags_str = str(flags[0][1])
                    is_seen_after = '\\Seen' in flags_str
                    print(f"   Seen flag AFTER RFC822 fetch: {is_seen_after}")

                # Now test UNSEEN search again
                print("   Testing UNSEEN search after fetch...")
                status, unseen_ids_after = connection.search(None, "UNSEEN")
                if status == 'OK':
                    unseen_count_after = len(unseen_ids_after[0].split()) if unseen_ids_after[0] else 0
                    print(f"   Found {unseen_count_after} unseen emails after fetch")
        
        # Close connection
        connection.logout()
        print("\n✅ Test completed successfully")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_email_connection()
