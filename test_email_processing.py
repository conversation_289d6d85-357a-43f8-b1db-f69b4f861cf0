#!/usr/bin/env python3
"""Test script to debug email processing issues."""

import sys
import os
import logging
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(__file__))

from email_agent.processor import EmailProcessor
from email_agent.database import EmailDatabase
from email_agent.connection import EmailConnection

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

def test_email_connection():
    """Test email connection and search."""
    print("🔍 Testing email connection...")
    
    connection = EmailConnection()
    
    # Test IMAP connection
    if connection.connect_imap():
        print("✅ IMAP connection successful")
        
        # Test getting emails
        database = EmailDatabase()
        last_sync = database.get_last_sync_timestamp()
        print(f"📅 Last sync timestamp: {last_sync}")
        
        emails = connection.get_new_emails(last_sync)
        print(f"📧 Found {len(emails)} emails")
        
        for i, email in enumerate(emails):
            print(f"  {i+1}. UID: {email['uid']}, From: {email['sender']}, Subject: {email['subject']}")
            
            # Check if already processed
            is_processed = database.is_email_processed(email['uid'])
            print(f"     Already processed: {is_processed}")
        
        connection.disconnect()
    else:
        print("❌ IMAP connection failed")

def test_email_processing():
    """Test full email processing."""
    print("\n🚀 Testing email processing...")
    
    processor = EmailProcessor()
    result = processor.process_new_emails()
    
    print(f"📊 Processing result: {result}")

def test_database_status():
    """Test database status."""
    print("\n💾 Testing database status...")
    
    database = EmailDatabase()
    stats = database.get_processing_stats()
    
    print(f"📈 Processing stats: {stats}")

def main():
    """Main test function."""
    print("🧪 Email Processing Debug Test")
    print("=" * 50)
    
    try:
        test_email_connection()
        test_email_processing()
        test_database_status()
        
        print("\n✅ All tests completed")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        print(f"❌ Test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n🛑 Interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
